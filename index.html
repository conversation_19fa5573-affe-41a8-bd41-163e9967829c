<!DOCTYPE html>
<html lang="id" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GesitWeb - Landing Page Profesional untuk UMKM</title>
    <meta name="description" content="Jasa pembuatan landing page profesional, cepat, dan terjangkau untuk UMKM dan bisnis kecil di Indonesia.">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2563EB',
                        'primary-dark': '#1E40AF',
                        secondary: '#E5E7EB',
                        'dark-gray': '#111827',
                        'light-bg': '#F9FAFB'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Custom Styles -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
            scroll-behavior: smooth;
        }
    </style>
</head>
<body class="bg-white text-dark-gray">
    <!-- Navbar Section -->
<header class="sticky top-0 z-50 bg-white border-b border-secondary">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
            <!-- Logo -->
            <div class="flex-shrink-0">
                <a href="#hero" class="text-2xl font-bold text-dark-gray hover:text-primary transition-colors duration-200">
                    Gesit<span class="text-primary hover:text-dark-gray transition-colors duration-200">Web</span>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <nav class="hidden md:block">
                <div class="ml-10 flex items-center space-x-8">
                    <a href="#proses" class="text-dark-gray hover:text-primary font-medium transition-colors duration-200">Proses</a>
                    <a href="#portofolio" class="text-dark-gray hover:text-primary font-medium transition-colors duration-200">Portofolio</a>
                    <a href="#harga" class="text-dark-gray hover:text-primary font-medium transition-colors duration-200">Harga</a>
                    <a href="https://wa.me/6281234567890?text%3A%20Halo%2C%20saya%20tertarik%20dengan%20layanan%20anda." class="bg-primary hover:bg-primary-dark text-white font-medium px-5 py-2 rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md">
                        Mulai Proyek Anda
                    </a>
                </div>
            </nav>

            <!-- Mobile menu button -->
            <div class="md:hidden flex items-center">
                <button id="mobile-menu-button" type="button" class="text-dark-gray hover:text-primary p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="hidden md:hidden bg-white border-t border-secondary">
        <div class="px-2 pt-2 pb-3 space-y-1">
            <a href="#proses" class="block px-3 py-2 text-dark-gray hover:text-primary font-medium rounded-md">Proses</a>
            <a href="#portofolio" class="block px-3 py-2 text-dark-gray hover:text-primary font-medium rounded-md">Portofolio</a>
            <a href="#harga" class="block px-3 py-2 text-dark-gray hover:text-primary font-medium rounded-md">Harga</a>
            <a href="https://wa.me/6281234567890?text%3A%20Halo%2C%20saya%20tertarik%20dengan%20layanan%20anda." class="block px-3 py-2 bg-primary hover:bg-primary-dark text-white font-medium rounded-md mt-2 mx-3 text-center">
                Mulai Proyek Anda
            </a>
        </div>
    </div>
</header>
    
    <main>
     <!-- Hero Section -->
<section class="relative py-20 md:py-32 overflow-hidden" id="hero">
    <!-- Background decoration elements -->
    <div class="absolute inset-0 bg-light-bg"></div>
    <div class="absolute top-20 right-0 w-64 h-64 bg-primary opacity-5 rounded-full blur-3xl"></div>
    <div class="absolute bottom-10 left-10 w-48 h-48 bg-primary opacity-3 rounded-full blur-2xl"></div>
    
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="max-w-5xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Text Content -->
                <div class="text-left">
                    <div class="inline-flex items-center px-4 py-2 bg-blue-50 rounded-full mb-6">
                        <span class="w-2 h-2 bg-primary rounded-full mr-3"></span>
                        <span class="text-primary font-medium text-sm">PROSES CEPAT & EFISIEN</span>
                    </div>
                    
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-extrabold leading-tight tracking-tight text-dark-gray mb-6">
                        Website Profesional, 
                        <span class="text-primary">Jadi dalam 48 Jam.</span>
                    </h1>
                    
                    <p class="text-xl text-dark-gray font-normal leading-relaxed mb-8 max-w-2xl">
                        Solusi landing page modern untuk UMKM dan freelancer yang butuh tampil kredibel di digital 
                        <span class="font-medium">tanpa ribet dan tanpa mahal.</span>
                    </p>
                    
                    <div class="flex flex-col sm:flex-row gap-4 mb-8">
                        <a href="#mulai" class="bg-primary hover:bg-primary-dark text-white font-semibold px-8 py-4 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-center whitespace-nowrap">
                            Mulai Proyek Anda
                        </a>
                        <a href="#portofolio" class="border-2 border-secondary hover:border-primary text-dark-gray hover:text-primary font-semibold px-8 py-4 rounded-lg transition-all duration-300 text-center whitespace-nowrap">
                            Lihat Portofolio
                        </a>
                    </div>
                    
                    <!-- Trust indicators -->
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <span class="ml-2 text-dark-gray font-medium">4.9/5 Rating</span>
                        </div>
                        <div class="flex items-center">
                            <span class="text-dark-gray font-medium">100+ Proyek</span>
                        </div>
                    </div>
                </div>
                
                <!-- Visual Element -->
                <div class="relative">
                    <div class="relative rounded-2xl overflow-hidden shadow-2xl">
                        <div class="bg-gradient-to-br from-primary to-primary-dark aspect-video flex items-center justify-center">
                            <div class="text-center p-8">
                                <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-6 inline-block">
                                    <div class="text-white">
                                        <div class="flex items-center justify-center mb-4">
                                            <div class="w-3 h-3 bg-red-400 rounded-full mr-2"></div>
                                            <div class="w-3 h-3 bg-yellow-400 rounded-full mr-2"></div>
                                            <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                        </div>
                                        <div class="text-2xl font-bold mb-2">GesitWeb</div>
                                        <div class="text-sm opacity-80">Landing Page Modern</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Floating elements -->
                    <div class="absolute -top-4 -right-4 bg-white rounded-lg shadow-lg p-4 border border-secondary">
                        <div class="text-xs text-gray-500 mb-1">Mulai dari</div>
                        <div class="text-2xl font-bold text-primary">Rp500K</div>
                    </div>
                    
                    <div class="absolute -bottom-4 -left-4 bg-white rounded-lg shadow-lg p-3 border border-secondary">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-sm font-medium">Online 48 Jam</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Keunggulan Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-3xl mx-auto text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-dark-gray mb-4">
                Kenapa Pilih GesitWeb?
            </h2>
            <p class="text-xl text-gray-600 font-normal">
                Solusi landing page yang dirancang khusus untuk kebutuhan UMKM dan freelancer Indonesia
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <!-- Keunggulan 1 -->
            <div class="bg-light-bg rounded-2xl p-8 hover:shadow-lg transition-all duration-300 border border-secondary border-opacity-50">
                <div class="w-16 h-16 bg-primary bg-opacity-10 rounded-xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-dark-gray mb-3">Proses Gesit</h3>
                <p class="text-gray-600 font-normal leading-relaxed">
                    Website Anda siap online hanya dalam 1-2 hari kerja.
                </p>
            </div>

            <!-- Keunggulan 2 -->
            <div class="bg-light-bg rounded-2xl p-8 hover:shadow-lg transition-all duration-300 border border-secondary border-opacity-50">
                <div class="w-16 h-16 bg-primary bg-opacity-10 rounded-xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-dark-gray mb-3">Harga Terjangkau</h3>
                <p class="text-gray-600 font-normal leading-relaxed">
                    Kualitas profesional dengan investasi yang ramah di kantong.
                </p>
            </div>

            <!-- Keunggulan 3 -->
            <div class="bg-light-bg rounded-2xl p-8 hover:shadow-lg transition-all duration-300 border border-secondary border-opacity-50">
                <div class="w-16 h-16 bg-primary bg-opacity-10 rounded-xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-dark-gray mb-3">Desain Modern</h3>
                <p class="text-gray-600 font-normal leading-relaxed">
                    Tampil kredibel dengan desain terkini yang responsif.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Proses Kerja Section -->
<section class="py-20 bg-light-bg" id="proses">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-3xl mx-auto text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-dark-gray mb-4">
                Proses Kerja yang Gesit
            </h2>
            <p class="text-xl text-gray-600 font-normal">
                Kami buat prosesnya sederhana agar hasilnya maksimal
            </p>
        </div>

        <div class="relative">
            <!-- Connecting line -->
            <div class="hidden md:block absolute top-16 left-12 right-12 h-0.5 bg-primary bg-opacity-20"></div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <!-- Langkah 1 -->
                <div class="relative pt-8">
                    <div class="absolute top-0 left-8 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold text-lg z-10">
                        01
                    </div>
                    <div class="bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-all duration-300 border border-secondary border-opacity-50 h-full">
                        <h3 class="text-2xl font-bold text-dark-gray mb-4">Briefing Cepat</h3>
                        <p class="text-gray-600 font-normal leading-relaxed">
                            Isi form singkat, kami akan pahami semua kebutuhan Anda.
                        </p>
                    </div>
                </div>

                <!-- Langkah 2 -->
                <div class="relative pt-8">
                    <div class="absolute top-0 left-8 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold text-lg z-10">
                        02
                    </div>
                    <div class="bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-all duration-300 border border-secondary border-opacity-50 h-full">
                        <h3 class="text-2xl font-bold text-dark-gray mb-4">Desain & Development</h3>
                        <p class="text-gray-600 font-normal leading-relaxed">
                            Kami wujudkan brief Anda menjadi desain dan kode berkualitas.
                        </p>
                    </div>
                </div>

                <!-- Langkah 3 -->
                <div class="relative pt-8">
                    <div class="absolute top-0 left-8 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold text-lg z-10">
                        03
                    </div>
                    <div class="bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-all duration-300 border border-secondary border-opacity-50 h-full">
                        <h3 class="text-2xl font-bold text-dark-gray mb-4">Revisi & Peluncuran</h3>
                        <p class="text-gray-600 font-normal leading-relaxed">
                            Satu kali revisi minor untuk memastikan semuanya sempurna sebelum website Anda live.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- CTA di bawah proses -->
        <div class="text-center mt-16">
            <a href="#mulai" class="inline-block bg-primary hover:bg-primary-dark text-white font-semibold px-8 py-4 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                Mulai Proyek Anda Sekarang
            </a>
            <p class="mt-4 text-gray-600">
                Proses mulai dari briefing hingga live hanya dalam 48 jam
            </p>
        </div>
    </div>
</section>

<!-- Portofolio Section -->
<section id="portofolio" class="py-20 bg-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-3xl mx-auto text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-dark-gray mb-4">
                Hasil Kerja Kami
            </h2>
            <p class="text-xl text-gray-600 font-normal">
                Landing page yang kami buat membantu bisnis tumbuh dan terlihat profesional
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
           <!-- Proyek 1 -->
            <div class="group overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 bg-white border border-secondary border-opacity-30">
                 <div class="aspect-video bg-gradient-to-br from-primary to-primary-dark opacity-90 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                     <div class="text-center text-white">
                        <img src="Untitled design (2).jpg" alt="Kopi Senja Website" class="max-h-full max-w-full object-contain">
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="inline-block px-3 py-1 bg-primary bg-opacity-10 text-primary text-xs font-semibold rounded-full mb-3">
                            UMKM F&B
                        </div>
                        <h3 class="text-xl font-bold text-dark-gray mb-2">Kopi Senja</h3>
                        <p class="text-gray-600 text-sm mb-4">Landing page untuk kedai kopi lokal dengan menu digital dan sistem pemesanan</p>
                        <a href="https://kopisenja-demo.gesitweb.com" target="_blank" 
                        class="inline-flex items-center text-primary hover:text-primary-dark font-semibold text-sm transition-colors duration-300">
                            <span>Lihat Website</span>
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                            </svg>
                        </a>
                    </div>
                </div>

            <!-- Proyek 2 -->
            <div class="group overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 bg-white border border-secondary border-opacity-30">
                <div class="aspect-video bg-gradient-to-br from-primary to-primary-dark opacity-90 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                    <div class="text-center text-white">
                       <img src="Untitled design (2).jpg" alt="Kopi Senja Website" class="max-h-full max-w-full object-contain">
                       </div>
                   </div>
                   <div class="p-6">
                       <div class="inline-block px-3 py-1 bg-primary bg-opacity-10 text-primary text-xs font-semibold rounded-full mb-3">
                           UMKM F&B
                       </div>
                       <h3 class="text-xl font-bold text-dark-gray mb-2">Kopi Senja</h3>
                       <p class="text-gray-600 text-sm mb-4">Landing page untuk kedai kopi lokal dengan menu digital dan sistem pemesanan</p>
                       <a href="https://kopisenja-demo.gesitweb.com" target="_blank" 
                       class="inline-flex items-center text-primary hover:text-primary-dark font-semibold text-sm transition-colors duration-300">
                           <span>Lihat Website</span>
                           <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                           </svg>
                       </a>
                   </div>
               </div>

            <!-- Proyek 3
            <div class="group overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 bg-white border border-secondary border-opacity-30">
                <div class="aspect-video bg-gradient-to-br from-amber-500 to-amber-700 opacity-90 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                    <div class="text-center text-white">
                        <div class="text-4xl font-bold mb-2">BT</div>
                        <div class="text-sm opacity-80">Batu Tahan</div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="inline-block px-3 py-1 bg-amber-100 text-amber-700 text-xs font-semibold rounded-full mb-3">
                        UMKM Manufaktur
                    </div>
                    <h3 class="text-xl font-bold text-dark-gray mb-2">Batu Tahan</h3>
                    <p class="text-gray-600 text-sm">Website e-commerce untuk produsen batu alam dengan katalog produk lengkap</p>
                </div>
            </div>

             Proyek 4
            <div class="group overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 bg-white border border-secondary border-opacity-30">
                <div class="aspect-video bg-gradient-to-br from-emerald-500 to-emerald-700 opacity-90 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                    <div class="text-center text-white">
                        <div class="text-4xl font-bold mb-2">SK</div>
                        <div class="text-sm opacity-80">Sehat Konsult</div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="inline-block px-3 py-1 bg-emerald-100 text-emerald-700 text-xs font-semibold rounded-full mb-3">
                        Konsultan Kesehatan
                    </div>
                    <h3 class="text-xl font-bold text-dark-gray mb-2">Sehat Konsult</h3>
                    <p class="text-gray-600 text-sm">Landing page untuk konsultan kesehatan dengan sistem appointment booking</p>
                </div>
            </div>  -->
        </div>

        <!-- CTA di bawah portofolio -->
        <div class="text-center mt-12">
            <a href="https://wa.me/6281280000000?text=Halo%20Gesit,%20saya%20tertarik%20membuat%20website%20seperti%20ini." class="inline-block bg-primary hover:bg-primary-dark text-white font-semibold px-8 py-4 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                Buat Website Seperti Ini
            </a>
        </div>
    </div>
</section>

<!-- Harga Section -->
<section id="harga" class="py-20 bg-light-bg">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-3xl mx-auto text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-dark-gray mb-4">
                Investasi yang Terjangkau
            </h2>
            <p class="text-xl text-gray-600 font-normal">
                Kualitas profesional dengan harga yang ramah untuk UMKM dan freelancer
            </p>
        </div>

        <div class="max-w-2xl mx-auto">
            <!-- Pricing Box -->
            <div class="bg-white rounded-3xl shadow-xl border border-secondary border-opacity-50 overflow-hidden transform hover:scale-[1.02] transition-all duration-300">
                <div class="p-8 md:p-12">
                    <!-- Badge -->
                    <div class="inline-block px-4 py-2 bg-primary bg-opacity-10 text-primary font-semibold rounded-full text-sm mb-6">
                        PAKET POPULER
                    </div>
                    
                    <!-- Judul Paket -->
                    <h3 class="text-3xl font-bold text-dark-gray mb-2">Paket Gesit</h3>
                    <p class="text-gray-600 mb-8">Solusi lengkap untuk kehadiran digital Anda</p>
                    
                    <!-- Harga -->
                    <div class="mb-8">
                        <div class="text-5xl md:text-6xl font-extrabold text-dark-gray mb-2">
                            Rp 500<span class="text-3xl">.000</span>
                        </div>
                        <div class="text-gray-500 font-medium">sekali bayar</div>
                    </div>
                    
                    <!-- Fitur List -->
                    <div class="mb-10">
                        <ul class="space-y-4">
                            <li class="flex items-start">
                                <svg class="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 font-medium">1 Halaman Landing Page</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 font-medium">Desain Responsif</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 font-medium">SEO Dasar</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 font-medium">Copywriting Dasar</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 font-medium">Hosting Gratis (Subdomain)</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700 font-medium">Konsultasi Gratis</span>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- CTA Button -->
                    <a href="https://wa.me/6281210810190?text=Halo%20Gesit,%20Saya%20tertarik%20dengan%20paket%20Gesit.%20Saya%20ingin%20mengetahui%20lebih%20lanjut." class="block w-full bg-primary hover:bg-primary-dark text-white font-bold text-lg py-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-center">
                        Pilih Paket Ini
                    </a>
                    
                    <!-- Garansi -->
                    <div class="mt-6 text-center">
                        <p class="text-sm text-gray-500 flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                            Garansi 10 hari setelah website online.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section dengan JavaScript untuk Single Open -->
<section id="faq" class="py-20 bg-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-3xl mx-auto text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-dark-gray mb-4">
                Pertanyaan Umum
            </h2>
            <p class="text-xl text-gray-600 font-normal">
                Temukan jawaban untuk pertanyaan yang sering diajukan
            </p>
        </div>

        <div class="max-w-3xl mx-auto space-y-4" id="faq-accordion">
            <!-- FAQ 1 -->
            <div class="faq-item group bg-light-bg rounded-2xl border border-secondary border-opacity-50 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="faq-header cursor-pointer p-6 flex items-center justify-between">
                    <h3 class="text-lg md:text-xl font-semibold text-dark-gray">Berapa lama proses pengerjaannya?</h3>
                    <div class="ml-4 flex-shrink-0">
                        <svg class="w-6 h-6 text-primary transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </div>
                <div class="faq-content hidden px-6 pb-6">
                    <p class="text-gray-600 leading-relaxed">
                        Proses pengerjaan hanya memakan waktu 1-2 hari kerja setelah briefing selesai. Kami fokus pada kecepatan tanpa mengorbankan kualitas.
                    </p>
                </div>
            </div>

            <!-- FAQ 2 -->
            <div class="faq-item group bg-light-bg rounded-2xl border border-secondary border-opacity-50 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="faq-header cursor-pointer p-6 flex items-center justify-between">
                    <h3 class="text-lg md:text-xl font-semibold text-dark-gray">Apakah saya bisa meminta revisi?</h3>
                    <div class="ml-4 flex-shrink-0">
                        <svg class="w-6 h-6 text-primary transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </div>
                <div class="faq-content hidden px-6 pb-6">
                    <p class="text-gray-600 leading-relaxed">
                        Ya, Anda berhak atas 2 kali revisi minor untuk memastikan hasil akhir sesuai dengan harapan Anda sebelum website live.
                    </p>
                </div>
            </div>

            <!-- FAQ 3 -->
            <div class="faq-item group bg-light-bg rounded-2xl border border-secondary border-opacity-50 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="faq-header cursor-pointer p-6 flex items-center justify-between">
                    <h3 class="text-lg md:text-xl font-semibold text-dark-gray">Apa saja yang perlu saya siapkan?</h3>
                    <div class="ml-4 flex-shrink-0">
                        <svg class="w-6 h-6 text-primary transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </div>
                <div class="faq-content hidden px-6 pb-6">
                    <p class="text-gray-600 leading-relaxed">
                        Cukup isi form briefing singkat yang kami sediakan. Kami akan memandu Anda untuk menyediakan logo, foto produk/jasa, dan informasi bisnis Anda.
                    </p>
                </div>
            </div>
        </div>

        <!-- Additional CTA -->
        <div class="text-center mt-12">
            <p class="text-gray-600 mb-6">Masih ada pertanyaan lain?</p>
            <a href="https://wa.me/6281234567890?text=Halo, saya ingin tanya tentang layanan website." target="_blank" class="inline-flex items-center bg-primary hover:bg-primary-dark text-white font-semibold px-6 py-3 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                Hubungi via WhatsApp
            </a>
        </div>
    </div>
</section>

<!-- Final CTA Section -->
<section id="mulai" class="py-20 bg-gradient-to-r from-primary to-primary-dark relative overflow-hidden">
    <!-- Background decoration -->
    <div class="absolute top-0 left-0 w-full h-full opacity-10">
        <div class="absolute top-10 right-10 w-32 h-32 bg-white rounded-full blur-2xl"></div>
        <div class="absolute bottom-10 left-10 w-48 h-48 bg-white rounded-full blur-3xl"></div>
    </div>
    
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="max-w-4xl mx-auto text-center">
            <h2 class="text-3xl md:text-4xl lg:text-5xl font-extrabold text-white mb-6 leading-tight">
                Siap Punya Website Profesional Pertama Anda?
            </h2>
            <p class="text-xl text-white text-opacity-90 font-normal mb-10 max-w-2xl mx-auto">
                Mulai perjalanan digital Anda dalam 48 jam. Cepat, mudah, dan terjangkau.
            </p>
            <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
                <a href="https://wa.me/6281234567890?text=Halo,%20saya%20tertarik%20memulai%20proyek%20landing%20page%20dengan%20GesitWeb.%20Mohon%20bantuan%20untuk%20memulai%20prosesnya.
                " class="w-full sm:w-auto bg-white text-primary hover:bg-gray-100 font-bold text-lg px-8 py-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-center whitespace-nowrap">
                    Mulai Proyek Anda Sekarang
                </a>
                <a href="https://wa.me/6281234567890?text=Halo,%20saya%20mau%20tanya%20dulu%20tentang%20layanan%20GesitWeb." target="_blank" class="w-full sm:w-auto border-2 border-white border-opacity-50 text-white hover:border-opacity-100 font-semibold px-8 py-4 rounded-xl transition-all duration-300 text-center whitespace-nowrap flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/>
                    </svg>
                    Chat via WhatsApp
                </a>
            </div>
        </div>
    </div>
</section>
    </main>
                <!-- Footer -->
<footer class="bg-dark-gray text-white pt-16 pb-8">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-12 mb-12">
            <!-- Logo dan Deskripsi -->
            <div class="md:col-span-1">
                <div class="text-2xl font-bold mb-4">GesitWeb</div>
                <p class="text-gray-300 font-normal leading-relaxed mb-6 max-w-md">
                    Layanan pembuatan landing page profesional yang cepat, mudah, dan terjangkau untuk UMKM dan freelancer Indonesia.
                </p>
                <div class="flex space-x-4">
                    <a href="https://instagram.com/gesitweb" target="_blank" class="text-gray-300 hover:text-white transition-colors duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12 0C8.74 0 8.333.015 7.053.072C5.775.132 4.905.333 4.14.63c-.789.306-1.459.717-2.126 1.384S.935 3.35.63 4.14C.333 4.905.131 5.775.072 7.053C.012 8.333 0 8.74 0 12s.015 3.667.072 4.947c.06 1.277.261 2.148.558 2.913a5.885 5.885 0 0 0 1.384 2.126A5.868 5.868 0 0 0 4.14 23.37c.766.296 1.636.499 2.913.558C8.333 23.988 8.74 24 12 24s3.667-.015 4.947-.072c1.277-.06 2.148-.262 2.913-.558a5.898 5.898 0 0 0 2.126-1.384a5.86 5.86 0 0 0 1.384-2.126c.296-.765.499-1.636.558-2.913c.06-1.28.072-1.687.072-4.947s-.015-3.667-.072-4.947c-.06-1.277-.262-2.149-.558-2.913a5.89 5.89 0 0 0-1.384-2.126A5.847 5.847 0 0 0 19.86.63c-.765-.297-1.636-.499-2.913-.558C15.667.012 15.26 0 12 0zm0 2.16c3.203 0 3.585.016 4.85.071c1.17.055 1.805.249 2.227.415c.562.217.96.477 1.382.896c.419.42.679.819.896 1.381c.164.422.36 1.057.413 2.227c.057 1.266.07 1.646.07 4.85s-.015 3.585-.074 4.85c-.061 1.17-.256 1.805-.421 2.227a3.81 3.81 0 0 1-.899 1.382a3.744 3.744 0 0 1-1.38.896c-.42.164-1.065.36-2.235.413c-1.274.057-1.649.07-4.859.07c-3.211 0-3.586-.015-4.859-.074c-1.171-.061-1.816-.256-2.236-.421a3.716 3.716 0 0 1-1.379-.899a3.644 3.644 0 0 1-.9-1.38c-.165-.42-.359-1.065-.42-2.235c-.045-1.26-.061-1.649-.061-4.844c0-3.196.016-3.586.061-4.861c.061-1.17.255-1.814.42-2.234c.21-.57.479-.96.9-1.381c.419-.419.81-.689 1.379-.898c.42-.166 1.051-.361 2.221-.421c1.275-.045 1.65-.06 4.859-.06l.045.03zm0 3.678a6.162 6.162 0 1 0 0 12.324a6.162 6.162 0 1 0 0-12.324zM12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4s4 1.79 4 4s-1.79 4-4 4zm7.846-10.405a1.441 1.441 0 0 1-2.88 0a1.44 1.44 0 0 1 2.88 0z"/></svg>
                    </a>
                    <a href="https://linkedin.com/company/gesitweb" target="_blank" class="text-gray-300 hover:text-white transition-colors duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M22 6v5q-4 0-6-2v7a7 7 0 1 1-5-6.7m0 6.7a2 2 0 1 0-2 2a2 2 0 0 0 2-2V1h5q2 5 6 5"/></svg>
                    </a>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="md:col-span-1">
                <h3 class="text-lg font-semibold mb-4">Tautan Cepat</h3>
                <ul class="space-y-3">
                    <li><a href="#proses" class="text-gray-300 hover:text-white transition-colors duration-300">Proses Kerja</a></li>
                    <li><a href="#portofolio" class="text-gray-300 hover:text-white transition-colors duration-300">Portofolio</a></li>
                    <li><a href="#harga" class="text-gray-300 hover:text-white transition-colors duration-300">Harga</a></li>
                    <li><a href="#faq" class="text-gray-300 hover:text-white transition-colors duration-300">FAQ</a></li>
                </ul>
            </div>

            <!-- Kontak -->
            <div class="md:col-span-1">
                <h3 class="text-lg font-semibold mb-4">Kontak</h3>
                <ul class="space-y-3 text-gray-300">
                    <li class="flex items-start">
                        <svg class="w-5 h-5 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <span><EMAIL></span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        <span>+62 812 3456 7890</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Copyright -->
        <div class="border-t border-gray-800 pt-8">
            <div class="flex flex-col md:flex-row items-center justify-between">
                <p class="text-gray-400 text-sm mb-4 md:mb-0">
                    © 2025 GesitWeb. All rights reserved.
                </p>
                <div class="flex space-x-6">
                    <a href="javascript:void(0)" onclick="openPrivacyModal()" class="text-gray-400 hover:text-gray-300 text-sm transition-colors duration-300">Kebijakan Privasi</a>
                    <a href="javascript:void(0)" onclick="openTermsModal()" class="text-gray-400 hover:text-gray-300 text-sm transition-colors duration-300">Syarat & Ketentuan</a>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Modal untuk Kebijakan Privasi -->
<div id="privacy-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        
        <!-- Modal content -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-2xl font-bold text-gray-900">Kebijakan Privasi</h3>
                    <button id="close-privacy-modal" class="text-gray-400 hover:text-gray-600">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="mt-2 max-h-96 overflow-y-auto pr-2">
                    <div class="text-gray-600 text-sm leading-relaxed">
                        <p>
                            Di GesitWeb, kami menghargai privasi Anda. Kebijakan Privasi ini menjelaskan bagaimana kami mengumpulkan, menggunakan, dan melindungi informasi pribadi yang Anda berikan kepada kami saat menggunakan layanan kami.
                            </p>
                            <p>
                            <strong>Informasi yang Kami Kumpulkan:</strong> Kami mengumpulkan informasi yang Anda berikan secara langsung melalui formulir briefing proyek, seperti nama, alamat email, dan nomor WhatsApp. Informasi ini kami kumpulkan semata-mata untuk tujuan memahami kebutuhan proyek Anda dan untuk berkomunikasi dengan Anda selama proses pengerjaan.
                            </p>
                            <p>
                            <strong>Penggunaan Informasi:</strong> Informasi pribadi Anda hanya akan digunakan untuk keperluan internal yang berkaitan dengan proyek Anda, termasuk komunikasi, pengiriman draf, proses administrasi seperti penagihan, dan untuk informasi yang akan digunakan untuk landing page. Kami berkomitmen untuk **tidak akan pernah** menjual, menyewakan, atau membagikan informasi pribadi Anda kepada pihak ketiga mana pun tanpa persetujuan eksplisit dari Anda.
                            </p>
                            <p>
                            <strong>Keamanan Data:</strong> Kami mengambil langkah-langkah yang wajar untuk melindungi informasi Anda dari akses yang tidak sah atau pengungkapan yang tidak semestinya. Namun, perlu diketahui bahwa tidak ada transmisi data melalui internet yang 100% aman.
                            </p>
                            <p>
                            Jika Anda memiliki pertanyaan lebih lanjut mengenai kebijakan privasi kami, jangan ragu untuk menghubungi kami.
                            </p>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button id="accept-privacy" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark sm:ml-3 sm:w-auto sm:text-sm">
                    Saya Mengerti
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal untuk Syarat & Ketentuan -->
<div id="terms-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        
        <!-- Modal content -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-2xl font-bold text-gray-900">Syarat & Ketentuan</h3>
                    <button id="close-terms-modal" class="text-gray-400 hover:text-gray-600">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
                <div class="mt-2 max-h-96 overflow-y-auto pr-2">
                    <div class="text-gray-600 text-sm leading-relaxed">
                        <p>
                            Dengan menggunakan layanan dari GesitWeb, Anda ("Klien") setuju untuk mematuhi Syarat dan Ketentuan ("Ketentuan") yang tercantum di bawah ini. Ketentuan ini dibuat untuk memastikan proses kerja yang jelas, adil, dan profesional bagi kedua belah pihak.
                            </p>
                            <p>
                            <strong>1. Lingkup Layanan:</strong> Layanan standar kami mencakup pembuatan satu halaman landing page statis, desain yang responsif di perangkat mobile dan desktop, optimasi SEO dasar, dan dua kali kesempatan revisi minor. Layanan tidak termasuk biaya pembelian domain custom, fitur kompleks seperti database, sistem login, atau e-commerce.
                            </p>
                            <p>
                            <strong>2. Kewajiban Klien:</strong> Klien bertanggung jawab untuk menyediakan semua materi yang diperlukan untuk proyek, termasuk namun tidak terbatas pada: teks (copywriting), logo, gambar produk, dan informasi kontak. Keterlambatan dalam penyediaan materi oleh Klien dapat mempengaruhi jadwal penyelesaian proyek.
                            </p>
                            <p>
                            <strong>3. Pembayaran:</strong> Proses pengerjaan proyek akan dimulai setelah pembayaran Uang Muka (DP) sebesar 50% dari total biaya diterima. Uang Muka (DP) bersifat tidak dapat dikembalikan (non-refundable). Sisa pembayaran sebesar 50% harus dilunasi setelah Klien menyetujui hasil akhir dan **sebelum** website diluncurkan secara publik atau file diserahterimakan.
                            </p>
                            <p>
                            <strong>4. Proses Revisi:</strong> Klien berhak atas dua kali revisi minor. Revisi minor mencakup perubahan pada teks, warna, atau penggantian gambar. Revisi yang bersifat mayor, seperti perubahan total tata letak (layout) atau penambahan seksi di luar kesepakatan awal, akan dikenakan biaya tambahan.
                            </p>
                            <p>
                            <strong>5. Hak Cipta & Portofolio:</strong> Setelah pembayaran lunas, hak cipta penuh atas desain dan kode website menjadi milik Klien. Namun, GesitWeb berhak untuk menampilkan hasil pekerjaan tersebut dalam portofolio kami untuk tujuan promosi.
                            </p>
                            <p>
                            <strong>6. Batasan Tanggung Jawab:</strong> Setelah serah terima proyek, GesitWeb tidak bertanggung jawab atas masalah teknis yang timbul akibat kesalahan pengelolaan oleh Klien atau masalah dari pihak ketiga (seperti penyedia hosting/domain), kecuali jika Klien berlangganan paket perawatan (maintenance) bulanan dari kami.
                            </p>
                            <p>
                            Dengan melakukan pembayaran DP, Klien dianggap telah membaca, memahami, dan menyetujui seluruh Syarat & Ketentuan ini.
                            </p>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button id="accept-terms" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark sm:ml-3 sm:w-auto sm:text-sm">
                    Saya Setuju
                </button>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript untuk Animasi dan Interaktivitas -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 1. Mobile Menu Toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        
        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });
            
            // Close mobile menu when clicking on links
            const mobileLinks = mobileMenu.querySelectorAll('a');
            mobileLinks.forEach(link => {
                link.addEventListener('click', () => {
                    mobileMenu.classList.add('hidden');
                });
            });
        }

        // 2. FAQ Accordion - Single Open
        const faqItems = document.querySelectorAll('.faq-item');
        
        faqItems.forEach(item => {
            const header = item.querySelector('.faq-header');
            const content = item.querySelector('.faq-content');
            const icon = item.querySelector('svg');
            
            if (header) {
                header.addEventListener('click', function() {
                    // Tutup semua item lain
                    faqItems.forEach(otherItem => {
                        if (otherItem !== item) {
                            const otherContent = otherItem.querySelector('.faq-content');
                            const otherIcon = otherItem.querySelector('svg');
                            
                            if (otherContent && !otherContent.classList.contains('hidden')) {
                                otherContent.classList.add('hidden');
                                if (otherIcon) {
                                    otherIcon.style.transform = 'rotate(0deg)';
                                }
                                otherItem.classList.remove('bg-white');
                                otherItem.classList.add('bg-light-bg');
                            }
                        }
                    });
                    
                    // Toggle item yang diklik
                    if (content) {
                        content.classList.toggle('hidden');
                        if (icon) {
                            icon.style.transform = content.classList.contains('hidden') ? 'rotate(0deg)' : 'rotate(180deg)';
                        }
                        
                        // Update background
                        if (content.classList.contains('hidden')) {
                            item.classList.remove('bg-white');
                            item.classList.add('bg-light-bg');
                        } else {
                            item.classList.remove('bg-light-bg');
                            item.classList.add('bg-white');
                        }
                    }
                });
            }
        });

        // 3. Smooth Scrolling untuk Anchor Links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    const offsetTop = targetElement.offsetTop - 80; // Adjust for navbar height
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // 4. Scroll Animation - Fade In Elements
        const animateOnScroll = function() {
            const elements = document.querySelectorAll('.scroll-animate');
            const windowHeight = window.innerHeight;
            const triggerPoint = windowHeight * 0.85; // Trigger when 85% of element is visible
            
            elements.forEach(element => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = elementTop < triggerPoint;
                
                if (elementVisible) {
                    element.classList.add('animate-fade-in-up');
                }
            });
        };

        // 5. Tambahkan class scroll-animate ke semua section untuk animasi
        const sections = document.querySelectorAll('section, header, .animate-section');
        sections.forEach((section, index) => {
            // Tambahkan delay berbeda untuk setiap section
            const delay = index * 100;
            section.classList.add('scroll-animate');
            section.style.transitionDelay = `${delay}ms`;
        });

        // 6. Tambahkan CSS untuk animasi (inline style)
        const style = document.createElement('style');
        style.textContent = `
            .scroll-animate {
                opacity: 0;
                transform: translateY(30px);
                transition: opacity 0.6s ease-out, transform 0.6s ease-out;
            }
            
            .animate-fade-in-up {
                opacity: 1 !important;
                transform: translateY(0) !important;
            }
            
            /* Ensure FAQ content transitions are smooth */
            .faq-content {
                transition: max-height 0.3s ease-out, opacity 0.3s ease-out;
                max-height: 0;
                opacity: 0;
            }
            
            .faq-content:not(.hidden) {
                max-height: 500px;
                opacity: 1;
            }
        `;
        document.head.appendChild(style);

        // 7. Jalankan animasi saat halaman dimuat dan saat scroll
        window.addEventListener('scroll', animateOnScroll);
        window.addEventListener('load', animateOnScroll);
        
        // Trigger sekali saat halaman dimuat untuk elemen yang sudah terlihat
        setTimeout(animateOnScroll, 100);

        // 8. Navbar Scroll Effect
        const header = document.querySelector('header');
        if (header) {
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    header.classList.add('shadow-md');
                    header.classList.add('bg-opacity-95');
                } else {
                    header.classList.remove('shadow-md');
                    header.classList.remove('bg-opacity-95');
                }
            });
        }

        // 9. Form Validation (jika ada form nanti)
        // Placeholder untuk form validation jika diperlukan

        // 10. Performance Optimization
        let ticking = false;
        const optimizedScroll = function() {
            if (!ticking) {
                requestAnimationFrame(function() {
                    animateOnScroll();
                    ticking = false;
                });
                ticking = true;
            }
        };

        window.addEventListener('scroll', optimizedScroll);

        // 11. Preload Animation untuk Hero Section
        const heroSection = document.querySelector('.hero-section') || document.querySelector('section:first-of-type');
        if (heroSection) {
            setTimeout(() => {
                heroSection.classList.add('animate-fade-in-up');
            }, 200);
        }
    });

    // 12. Utility Functions
    window.gesitWeb = {
        // Function untuk scroll ke section tertentu
        scrollToSection: function(sectionId) {
            const element = document.querySelector(sectionId);
            if (element) {
                const offsetTop = element.offsetTop - 80;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        },
        
        // Function untuk toggle mobile menu
        toggleMobileMenu: function() {
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.toggle('hidden');
            }
        },
        
        // Function untuk tracking events (placeholder)
        trackEvent: function(eventName, data) {
            console.log('Event tracked:', eventName, data);
            // Implement analytics tracking here
        }
    };
    // Function untuk membuka modal
    function openPrivacyModal() {
        const modal = document.getElementById('privacy-modal');
        if (modal) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden'; // Mencegah scroll background
        }
    }
    
    function openTermsModal() {
        const modal = document.getElementById('terms-modal');
        if (modal) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
    }
    
    // Function untuk menutup modal
    function closePrivacyModal() {
        const modal = document.getElementById('privacy-modal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.style.overflow = ''; // Kembalikan scroll
        }
    }
    
    function closeTermsModal() {
        const modal = document.getElementById('terms-modal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.style.overflow = '';
        }
    }
    
    // Event listeners untuk tombol close
    document.addEventListener('DOMContentLoaded', function() {
        // Close button privacy modal
        const closePrivacyBtn = document.getElementById('close-privacy-modal');
        const acceptPrivacyBtn = document.getElementById('accept-privacy');
        if (closePrivacyBtn) closePrivacyBtn.addEventListener('click', closePrivacyModal);
        if (acceptPrivacyBtn) acceptPrivacyBtn.addEventListener('click', closePrivacyModal);
        
        // Close button terms modal
        const closeTermsBtn = document.getElementById('close-terms-modal');
        const acceptTermsBtn = document.getElementById('accept-terms');
        if (closeTermsBtn) closeTermsBtn.addEventListener('click', closeTermsModal);
        if (acceptTermsBtn) acceptTermsBtn.addEventListener('click', closeTermsModal);
        
        // Close dengan ESC key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePrivacyModal();
                closeTermsModal();
            }
        });
        
        // Close dengan klik di luar modal
        const privacyModal = document.getElementById('privacy-modal');
        const termsModal = document.getElementById('terms-modal');
        
        if (privacyModal) {
            privacyModal.addEventListener('click', function(e) {
                if (e.target === privacyModal) closePrivacyModal();
            });
        }
        
        if (termsModal) {
            termsModal.addEventListener('click', function(e) {
                if (e.target === termsModal) closeTermsModal();
            });
        }
    });
</script>
</body>
</html>